import { type MetaFunction } from 'react-router';
import { useEffect } from 'react';

export const meta: MetaFunction = () => {
  return [
    { title: 'Our Story | Big River Coffee' },
    { description: 'Big River Coffee is more than just a coffee brand; it\'s an experience that combines the rugged charm of the Outdoors with the rich and delightful flavors of Latin American coffees.' }
  ];
};

export default function OurStoryPage() {
  useEffect(() => {
    if (typeof window === 'undefined') return;

    // Add body class for page-specific styling
    document.body.classList.add('our-story');

    // Ensure videos play properly
    const videos = document.querySelectorAll('video');
    videos.forEach(video => {
      video.play().catch(error => {
        console.log('Video autoplay prevented:', error);
      });
    });

    return () => {
      document.body.classList.remove('our-story');
    };
  }, []);

  return (
    <>
    <style>{`
      body.our-story {
        margin: 0 !important;
        padding: 0 !important;
        overflow-x: hidden;
      }
      body.our-story main {
        padding: 0 !important;
        margin: 0 !important;
      }
    `}</style>

      {/* Hero Video Section - Desktop Only */}
      <div className="hidden sm:block sticky top-0 w-full h-screen z-0">
        {/* Desktop Hero Video */}
        <div className="w-full h-full">
          <video
            src="/newhomepage/aboutus_hero_video.mp4"
            autoPlay
            muted
            loop
            playsInline
            preload="auto"
            controls={false}
            className="w-full h-full object-cover"
            style={{
              width: '100%',
              height: '100%',
              display: 'block'
            }}
            onLoadStart={() => console.log('Video loading started')}
            onCanPlay={() => console.log('Video can play')}
            onError={(e) => console.error('Video error:', e)}
          >
            <source src="/newhomepage/aboutus_hero_video.mp4" type="video/mp4" />
            Your browser does not support the video tag.
          </video>
        </div>
      </div>

      {/* Story Content Section */}
      <div className="relative z-10 min-h-screen border-t-8 border-army-600" style={{ backgroundColor: '#f97316', marginTop: 'calc(-50vh)' }}>

        {/* Hero Text Section */}
        <div className="py-20 relative overflow-hidden">
          {/* Background decorative circles */}
          <div className="absolute top-10 left-10 w-32 h-32 bg-army-600 rounded-full opacity-20"></div>
          <div className="absolute bottom-10 right-10 w-24 h-24 bg-army-600 rounded-full opacity-30"></div>
          <div className="absolute top-1/2 left-1/4 w-16 h-16 bg-army-600 rounded-full opacity-15"></div>
          <div className="absolute top-20 right-1/4 w-20 h-20 bg-army-600 rounded-full opacity-25"></div>

          <div className="max-w-4xl mx-auto px-6 text-center relative z-10">
            <h1 className="text-6xl lg:text-8xl font-bold text-white mb-8 tracking-tight">
              OUR STORY
            </h1>
            <div className="w-24 h-1 bg-army-600 mx-auto mb-8"></div>
            <p className="text-xl lg:text-2xl text-white leading-relaxed">
              Discover the story behind every cup - where adventure meets exceptional coffee, and every sip supports the communities that make it possible.
            </p>
          </div>
        </div>

        {/* About Us Section 1 */}
        <div className="py-16 relative overflow-hidden">
          {/* Decorative circles */}
          <div className="absolute top-8 left-8 w-28 h-28 bg-army-600 rounded-full opacity-15"></div>
          <div className="absolute bottom-12 right-12 w-20 h-20 bg-army-600 rounded-full opacity-25"></div>
          <div className="absolute top-1/3 right-1/4 w-12 h-12 bg-army-600 rounded-full opacity-20"></div>
          <div className="absolute bottom-1/4 left-1/3 w-16 h-16 bg-army-600 rounded-full opacity-18"></div>

          <div className="absolute left-0 top-1/2 transform -translate-y-1/2 w-2 h-32 bg-army-600 rounded-r-lg"></div>
          <div className="max-w-7xl mx-auto px-6 relative z-10">
            <div className="grid lg:grid-cols-2 gap-16 items-center">
              <div className="order-2 lg:order-1">
                <h2 className="text-4xl lg:text-5xl font-bold text-white mb-8">
                  Big River Coffee
                </h2>
                <p className="text-lg text-white leading-relaxed mb-6">
                  Big River Coffee is more than just a coffee brand; it's an experience that combines the rugged charm of the Outdoors with the rich and delightful flavors of Latin American coffees.
                </p>
                <p className="text-lg text-white leading-relaxed mb-6">
                  As part of the Good Coffee Co conglomerate, we are driven by the belief that coffee should not only taste exceptional but should also be a catalyst for positive change.
                </p>
                <p className="text-xl font-semibold text-army-200 bg-army-600 px-4 py-2 rounded-lg inline-block">
                  Thanks for being a part of the Big River Coffee family!
                </p>
              </div>
              <div className="order-1 lg:order-2">
                <div className="relative overflow-hidden rounded-2xl shadow-2xl border-4 border-white">
                  <img
                    src="/newhomepage/aboutus1.png"
                    alt="Big River Coffee story"
                    className="w-full h-auto object-cover transform hover:scale-105 transition-transform duration-700"
                  />
                  <div className="absolute top-4 right-4 w-8 h-8 bg-army-600 rounded-full"></div>
                </div>
              </div>
            </div>
          </div>
        </div>


        {/* About Us Section 2 */}
        <div className="py-16 relative overflow-hidden">
          {/* Decorative circles */}
          <div className="absolute top-12 right-8 w-24 h-24 bg-army-600 rounded-full opacity-20"></div>
          <div className="absolute bottom-8 left-16 w-32 h-32 bg-army-600 rounded-full opacity-15"></div>
          <div className="absolute top-1/4 left-8 w-14 h-14 bg-army-600 rounded-full opacity-25"></div>
          <div className="absolute bottom-1/3 right-1/4 w-18 h-18 bg-army-600 rounded-full opacity-22"></div>

          <div className="absolute right-0 top-1/2 transform -translate-y-1/2 w-2 h-32 bg-army-600 rounded-l-lg"></div>
          <div className="max-w-7xl mx-auto px-6 relative z-10">
            <div className="grid lg:grid-cols-2 gap-16 items-center">
              <div>
                <div className="relative overflow-hidden rounded-2xl shadow-2xl border-4 border-white">
                  <img
                    src="/newhomepage/aboutus2.png"
                    alt="Contributing to the common good"
                    className="w-full h-auto object-cover transform hover:scale-105 transition-transform duration-700"
                  />
                  <div className="absolute top-4 left-4 w-8 h-8 bg-army-600 rounded-full"></div>
                </div>
              </div>
              <div>
                <h2 className="text-4xl lg:text-5xl font-bold text-white mb-8">
                  Contributing to the Common Good
                </h2>
                <div className="w-16 h-1 bg-army-600 mb-6"></div>
                <p className="text-lg text-white leading-relaxed mb-6">
                  Our contribution is like a river that flows from the origin to the morning table of our customers—a coffee loaded with values and perfectly traceable.
                </p>
                <p className="text-lg text-white leading-relaxed">
                  Through Big River Coffee, you can discover the adventures that coffee families have lived for the last 30 years, all to keep alive the tradition, culture, and passion for coffee.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* About Us Section 3 */}
        <div className="py-16 relative overflow-hidden">
          {/* Decorative circles */}
          <div className="absolute top-16 left-12 w-26 h-26 bg-army-600 rounded-full opacity-18"></div>
          <div className="absolute bottom-16 right-8 w-22 h-22 bg-army-600 rounded-full opacity-28"></div>
          <div className="absolute top-1/3 right-1/3 w-10 h-10 bg-army-600 rounded-full opacity-20"></div>
          <div className="absolute bottom-1/4 left-1/4 w-14 h-14 bg-army-600 rounded-full opacity-16"></div>
          <div className="absolute top-8 right-16 w-8 h-8 bg-army-600 rounded-full opacity-30"></div>

          <div className="absolute left-0 top-1/2 transform -translate-y-1/2 w-2 h-32 bg-army-600 rounded-r-lg"></div>
          <div className="max-w-7xl mx-auto px-6 relative z-10">
            <div className="grid lg:grid-cols-2 gap-16 items-center">
              <div className="order-2 lg:order-1">
                <h2 className="text-4xl lg:text-5xl font-bold text-white mb-8">
                  The Freedom to Help
                </h2>
                <div className="w-16 h-1 bg-army-600 mb-6"></div>
                <p className="text-lg text-white leading-relaxed mb-6">
                  It's like morning landscapes or a mountain peak—every time you shake someone's hand to achieve that long-awaited peak, the same thing happens when you buy or subscribe to Big River Coffee.
                </p>
                <p className="text-lg text-white leading-relaxed">
                  This same feeling of freedom and genuine help carries over to the beautiful mountains from which our coffee is selected. Our special connections with Nicaragua have made us work closely with the Good Coffee Beans Cooperative to obtain high-quality beans grown at more than 5,000 feet above sea level.
                </p>
              </div>
              <div className="order-1 lg:order-2">
                <div className="relative overflow-hidden rounded-2xl shadow-2xl border-4 border-white">
                  <img
                    src="/newhomepage/aboutus3.png"
                    alt="The Freedom to Help"
                    className="w-full h-auto object-cover transform hover:scale-105 transition-transform duration-700"
                  />
                  <div className="absolute bottom-4 right-4 w-8 h-8 bg-army-600 rounded-full"></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Call to Action Section */}
        <div className="py-20 bg-gradient-to-r from-amber-500 to-orange-500 relative overflow-hidden">
          {/* Decorative circles */}
          <div className="absolute top-8 left-8 w-20 h-20 bg-white rounded-full opacity-10"></div>
          <div className="absolute bottom-8 right-8 w-24 h-24 bg-white rounded-full opacity-15"></div>
          <div className="absolute top-1/2 left-16 w-12 h-12 bg-white rounded-full opacity-20"></div>
          <div className="absolute bottom-16 left-1/3 w-16 h-16 bg-white rounded-full opacity-12"></div>
          <div className="absolute top-12 right-1/4 w-14 h-14 bg-white rounded-full opacity-18"></div>

          <div className="max-w-4xl mx-auto px-6 text-center relative z-10">
            <h2 className="text-4xl lg:text-5xl font-bold text-white mb-8">
              Ready to Taste the Adventure?
            </h2>
            <div className="mb-12 flex justify-center">
              <p className="text-xl text-white/90 max-w-2xl leading-relaxed text-center">
                Join thousands of coffee lovers who have discovered the perfect blend of adventure and exceptional taste. Every cup supports the communities that make it possible.
              </p>
            </div>
            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
              <a
                href="/collections/all"
                className="inline-flex items-center px-8 py-4 bg-white text-amber-600 rounded-xl font-semibold text-lg hover:bg-gray-50 hover:scale-105 transition-all duration-300 shadow-lg"
              >
                <span>Shop Our Coffee</span>
                <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                </svg>
              </a>
              <a
                href="/collections/all?section=subscriptions"
                className="inline-flex items-center px-8 py-4 bg-army-600 text-white rounded-xl font-semibold text-lg hover:bg-army-700 hover:scale-105 transition-all duration-300 shadow-lg"
              >
                <span>Start Subscription</span>
                <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
              </a>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
