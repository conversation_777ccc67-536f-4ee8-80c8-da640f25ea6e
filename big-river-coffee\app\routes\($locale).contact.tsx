import {Form, useActionData, useNavigation, Link, type MetaFunction} from 'react-router';
import {type ActionFunctionArgs} from '@shopify/remix-oxygen';
import {useState, useEffect} from 'react';

// Type definitions for better type safety
interface ContactFormData {
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  subject: string;
  message: string;
}

interface ActionData {
  success?: boolean;
  errors?: Record<string, string>;
  error?: string;
}

// Use the non-deprecated json function
const json = (data: any, init?: ResponseInit) => {
  return new Response(JSON.stringify(data), {
    ...init,
    headers: {
      'Content-Type': 'application/json',
      ...init?.headers,
    },
  });
};

export const meta: MetaFunction = () => {
  return [
    { title: 'Contact Us | Big River Coffee' },
    { description: 'At Big River Coffee, we love hearing from our customers and value your feedback. Contact us for questions, comments, or assistance.' }
  ];
};

export async function action({request, context}: ActionFunctionArgs): Promise<Response> {
  const admin = context.admin as any;
  // Parse form data
  const formData = await request.formData();
  const firstName = formData.get('first-name') as string;
  const lastName = formData.get('last-name') as string;
  const email = formData.get('email') as string;
  const phone = formData.get('phone') as string || '';
  const subject = formData.get('subject') as string;
  const message = formData.get('message') as string;

  // Enhanced validation with email format check
  const errors: Record<string, string> = {};
  if (!firstName?.trim()) errors['first-name'] = 'First name is required';
  if (!lastName?.trim()) errors['last-name'] = 'Last name is required';
  if (!email?.trim()) {
    errors['email'] = 'Email is required';
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
    errors['email'] = 'Please enter a valid email address';
  }
  if (!subject?.trim()) errors['subject'] = 'Subject is required';
  if (!message?.trim()) errors['message'] = 'Message is required';

  if (Object.keys(errors).length) {
    return json({success: false, errors}, {status: 400});
  }

  try {
    // Create metaobject entry using Admin API
    const data = await admin.request(`
      mutation CreateContactFormSubmission($input: MetaobjectCreateInput!) {
        metaobjectCreate(metaobject: $input) {
          metaobject {
            id
            handle
          }
          userErrors {
            field
            message
            code
          }
        }
      }
    `, {
      variables: {
        input: {
          type: "contact_form",
          fields: [
            { key: "first_name", value: firstName.trim() },
            { key: "last_name", value: lastName.trim() },
            { key: "email", value: email.trim().toLowerCase() },
            { key: "phone", value: phone.trim() },
            { key: "subject", value: subject.trim() },
            { key: "message", value: message.trim() },
            { key: "submitted_at", value: new Date().toISOString() }
          ]
        }
      }
    });

    console.log('Admin API response:', data);

    // Check for user errors from the mutation
    if (data.data?.metaobjectCreate?.userErrors?.length > 0) {
      const userErrors = data.data.metaobjectCreate.userErrors;
      console.error('User errors:', userErrors);
      console.error('Detailed user errors:', JSON.stringify(userErrors, null, 2));

      // Map user errors to form field errors if possible
      const fieldErrors: Record<string, string> = {};
      userErrors.forEach((error: any) => {
        if (error.field) {
          fieldErrors[error.field] = error.message;
        }
      });

      return json({
        success: false,
        errors: Object.keys(fieldErrors).length > 0 ? fieldErrors : undefined,
        error: Object.keys(fieldErrors).length === 0 ? 'Failed to submit form. Please check your information and try again.' : undefined
      }, { status: 400 });
    }

    // Success case
    if (data.data?.metaobjectCreate?.metaobject?.id) {
      console.log('Contact form submitted successfully:', data.data.metaobjectCreate.metaobject.id);
      return json({ success: true });
    }

    // Fallback error case
    return json({
      success: false,
      error: 'Unexpected response from server. Please try again.'
    }, { status: 500 });

  } catch (error) {
    console.error('Error creating contact form submission:', error);
    return json({
      success: false,
      error: 'Unable to submit form at this time. Please try again later or contact us directly.'
    }, { status: 500 });
  }
}

export default function ContactPage() {
  const actionData = useActionData<ActionData>();
  const navigation = useNavigation();
  const isSubmitting = navigation.state === 'submitting';
  const [formSubmitted, setFormSubmitted] = useState(false);

  // Update form submitted state when action completes successfully
  useEffect(() => {
    if (actionData?.success && !formSubmitted) {
      setFormSubmitted(true);
    }
  }, [actionData?.success, formSubmitted]);

  // Add body class for borderless styling
  useEffect(() => {
    if (typeof window === 'undefined') return;

    document.body.classList.add('contact');

    return () => {
      document.body.classList.remove('contact');
    };
  }, []);

  // Show success message if form was submitted successfully
  if (actionData?.success || formSubmitted) {
    return (
      <div className="min-h-screen" style={{ backgroundColor: '#f97316' }}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="max-w-3xl mx-auto">
            <div className="bg-green-50 border border-green-200 rounded-xl p-8 text-center">
              <svg className="w-16 h-16 text-green-500 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
              <h3 className="text-2xl font-bold text-gray-900 mb-2">Thank You!</h3>
              <p className="text-lg text-gray-600 mb-6">
                Your message has been submitted successfully. We'll get back to you shortly.
              </p>
              <Link
                to="/"
                className="inline-flex items-center px-6 py-3 bg-army-600 text-white rounded-lg font-medium hover:bg-army-700 transition-all duration-300 transform hover:scale-105 hover:shadow-lg"
              >
                Return to Homepage
                <svg className="ml-2 w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                </svg>
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
    <style>{`
      body.contact {
        margin: 0 !important;
        padding: 0 !important;
        overflow-x: hidden;
      }
      body.contact main {
        padding: 0 !important;
        margin: 0 !important;
      }
    `}</style>

    <div className="min-h-screen relative overflow-hidden" style={{
      backgroundColor: '#f97316',
      margin: 0,
      padding: 0,
      width: '100vw',
      minHeight: '100vh'
    }}>
      {/* Background decorative circles */}
      <div className="absolute top-20 left-10 w-32 h-32 bg-army-600 rounded-full opacity-20"></div>
      <div className="absolute bottom-40 right-10 w-24 h-24 bg-army-600 rounded-full opacity-30"></div>
      <div className="absolute top-1/3 left-1/4 w-16 h-16 bg-army-600 rounded-full opacity-15"></div>
      <div className="absolute top-1/2 right-1/4 w-20 h-20 bg-army-600 rounded-full opacity-25"></div>
      <div className="absolute bottom-1/4 left-1/3 w-12 h-12 bg-army-600 rounded-full opacity-20"></div>
      <div className="absolute top-3/4 right-1/3 w-28 h-28 bg-army-600 rounded-full opacity-15"></div>

      <div className="w-full px-4 sm:px-6 lg:px-8 py-16 relative z-10">
        <div className="w-full">
          <div className="text-center mb-12">
            <div className="bg-army-600 rounded-2xl p-8 mb-8">
              <span className="text-army-600 font-semibold text-sm uppercase tracking-wider bg-white px-4 py-1 rounded-full shadow-sm inline-block mb-4">Get In Touch</span>
              <h1 className="text-4xl font-bold text-white mb-4">Contact Us</h1>
              <div className="w-24 h-1 bg-white rounded mb-6 mx-auto"></div>
              <div className="flex justify-center">
                <div className="text-center max-w-2xl">
                  <p className="text-white text-lg leading-relaxed">
                    We'd love to hear from you! Whether you have a question about our products,
                    need help with an order, or just want to say hello, please fill out the form below
                    and we'll get back to you as soon as possible.
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="mb-12 text-center">
            <div className="bg-army-600 rounded-xl overflow-hidden py-10">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8 w-full px-4">
                <div className="flex flex-col items-center">
                  <div className="w-16 h-16 rounded-full bg-white flex items-center justify-center mb-4">
                    <svg className="w-8 h-8 text-army-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-medium text-white mb-2">Email</h3>
                  <p className="text-white"><EMAIL></p>
                </div>

                <div className="flex flex-col items-center">
                  <div className="w-16 h-16 rounded-full bg-white flex items-center justify-center mb-4">
                    <svg className="w-8 h-8 text-army-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-medium text-white mb-2">Questions?</h3>
                  <p className="text-white">We're here to help with any inquiries</p>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl overflow-hidden">
            <div className="p-8 md:p-10 flex justify-center">
              <Form method="post" className="space-y-6 w-full">
                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                  <div>
                    <label htmlFor="first-name" className="block text-sm font-medium text-army-600 text-left mb-2">
                      First name
                    </label>
                    <input
                      type="text"
                      name="first-name"
                      id="first-name"
                      autoComplete="given-name"
                      className={`mt-1 block w-full rounded-lg border-white shadow-sm focus:border-orange-500 focus:ring-orange-500 py-3 text-center ${
                        actionData?.errors?.['first-name'] ? 'border-red-500' : ''
                      }`}
                      placeholder="Your first name"
                      required
                    />
                    {actionData?.errors?.['first-name'] && (
                      <p className="text-red-500 text-sm mt-1">{actionData.errors['first-name']}</p>
                    )}
                  </div>

                  <div>
                    <label htmlFor="last-name" className="block text-sm font-medium text-army-600 text-left mb-2">
                      Last name
                    </label>
                    <input
                      type="text"
                      name="last-name"
                      id="last-name"
                      autoComplete="family-name"
                      className={`mt-1 block w-full rounded-lg border-gray-300 shadow-sm focus:border-army-600 focus:ring-army-600 py-3 text-center ${
                        actionData?.errors?.['last-name'] ? 'border-red-500' : ''
                      }`}
                      placeholder="Your last name"
                      required
                    />
                    {actionData?.errors?.['last-name'] && (
                      <p className="text-red-500 text-sm mt-1">{actionData.errors['last-name']}</p>
                    )}
                  </div>
                </div>

                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-army-600 text-left mb-2">
                    Email
                  </label>
                  <input
                    type="email"
                    name="email"
                    id="email"
                    autoComplete="email"
                    className={`mt-1 block w-full rounded-lg border-gray-300 shadow-sm focus:border-army-600 focus:ring-army-600 py-3 text-center ${
                      actionData?.errors?.['email'] ? 'border-red-500' : ''
                    }`}
                    placeholder="<EMAIL>"
                    required
                  />
                  {actionData?.errors?.['email'] && (
                    <p className="text-red-500 text-sm mt-1">{actionData.errors['email']}</p>
                  )}
                </div>

                <div>
                  <label htmlFor="phone" className="block text-sm font-medium text-army-600 text-left mb-2">
                    Phone (optional)
                  </label>
                  <input
                    type="tel"
                    name="phone"
                    id="phone"
                    autoComplete="tel"
                    className="mt-1 block w-full rounded-lg border-gray-300 shadow-sm focus:border-army-600 focus:ring-army-600 py-3 text-center"
                    placeholder="(*************"
                  />
                </div>

                <div>
                  <label htmlFor="subject" className="block text-sm font-medium text-army-600 text-left mb-2">
                    Subject
                  </label>
                  <select
                    id="subject"
                    name="subject"
                    className="mt-1 block w-full rounded-lg border-gray-300 shadow-sm focus:border-army-600 focus:ring-army-600 py-3 text-center appearance-none"
                    defaultValue="Order Inquiry"
                  >
                    <option>Order Inquiry</option>
                    <option>Product Question</option>
                    <option>Brewing Help</option>
                    <option>Feedback</option>
                    <option>Other</option>
                  </select>
                </div>

                <div>
                  <label htmlFor="message" className="block text-sm font-medium text-army-600 text-left mb-2">
                    Message
                  </label>
                  <textarea
                    id="message"
                    name="message"
                    rows={5}
                    className={`mt-1 block w-full rounded-lg border-gray-300 shadow-sm focus:border-army-600 focus:ring-army-600 text-center ${
                      actionData?.errors?.['message'] ? 'border-red-500' : ''
                    }`}
                    placeholder="Your message here..."
                    required
                  ></textarea>
                  {actionData?.errors?.['message'] && (
                    <p className="text-red-500 text-sm mt-1">{actionData.errors['message']}</p>
                  )}
                </div>

                <div className="pt-4">
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="w-full flex justify-center py-4 px-6 border border-transparent rounded-lg shadow-sm text-base font-medium text-white bg-army-600 hover:bg-army-700 transition-colors duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isSubmitting ? 'Sending...' : 'Send Message'}
                  </button>
                </div>

                {actionData?.error && (
                  <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                    {actionData.error}
                  </div>
                )}
              </Form>
            </div>
          </div>
        </div>
      </div>
    </div>
    </>
  );
}
