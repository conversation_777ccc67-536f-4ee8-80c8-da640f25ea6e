import {type LoaderFunctionArgs} from '@shopify/remix-oxygen';
import {Await, useLoaderData, Link, type MetaFunction} from 'react-router';
import {Suspense, useState, useEffect} from 'react';
import {Image, Money} from '@shopify/hydrogen';
import type {
  FeaturedCollectionFragment,
  RecommendedProductsQuery,
} from 'storefrontapi.generated';
import {ProductItem} from '~/components/ProductItem';
import {VideoIntro} from '~/components/VideoIntro';
import {OptimizedVideo} from '~/components/OptimizedVideo';

export const meta: MetaFunction = () => {
  return [{title: 'Big River Coffee | Premium Coffee for Adventurers'}];
};

// Custom hook for window size
function useWindowSize() {
  const [windowSize, setWindowSize] = useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 1024,
  });

  useEffect(() => {
    function handleResize() {
      setWindowSize({
        width: window.innerWidth,
      });
    }

    if (typeof window !== 'undefined') {
      window.addEventListener('resize', handleResize);
      handleResize();
      return () => window.removeEventListener('resize', handleResize);
    }
  }, []);

  return windowSize;
}

export async function loader(args: LoaderFunctionArgs) {
  // Start fetching non-critical data without blocking time to first byte
  const deferredData = loadDeferredData(args);

  // Await the critical data required to render initial state of the page
  const criticalData = await loadCriticalData(args);

  return {...deferredData, ...criticalData};
}

/**
 * Load data necessary for rendering content above the fold. This is the critical data
 * needed to render the page. If it's unavailable, the whole page should 400 or 500 error.
 */
async function loadCriticalData({context}: LoaderFunctionArgs) {
  const [{collections}] = await Promise.all([
    context.storefront.query(FEATURED_COLLECTION_QUERY),
    // Add other queries here, so that they are loaded in parallel
  ]);

  return {
    featuredCollection: collections.nodes[0],
  };
}

/**
 * Load data for rendering content below the fold. This data is deferred and will be
 * fetched after the initial page load. If it's unavailable, the page should still 200.
 * Make sure to not throw any errors here, as it will cause the page to 500.
 */
function loadDeferredData({context}: LoaderFunctionArgs) {
  const recommendedProducts = context.storefront
    .query(RECOMMENDED_PRODUCTS_QUERY)
    .catch((error) => {
      // Log query errors, but don't throw them so the page can still render
      console.error(error);
      return null;
    });

  return {
    recommendedProducts,
  };
}

export default function Homepage() {
  const data = useLoaderData<typeof loader>();
  const { width } = useWindowSize();
  const [showVideoIntro, setShowVideoIntro] = useState(true);
  const [hasSeenVideo, setHasSeenVideo] = useState(false);
  const [isVideoFadingOut, setIsVideoFadingOut] = useState(false);
  const [isHomepageFadingIn, setIsHomepageFadingIn] = useState(false);

  useEffect(() => {
    // Check if user has already seen the video in this session
    const videoSeen = sessionStorage.getItem('bigRiverVideoIntroSeen');
    if (videoSeen) {
      setShowVideoIntro(false);
      setHasSeenVideo(true);
      setIsHomepageFadingIn(true);
    }
  }, []);

  const handleVideoEnd = () => {
    setIsVideoFadingOut(true);
    // Wait for video fade out, then start homepage fade in sequence
    setTimeout(() => {
      setShowVideoIntro(false);
      // Small delay before starting homepage fade in
      setTimeout(() => {
        setIsHomepageFadingIn(true);
        setHasSeenVideo(true);
        sessionStorage.setItem('bigRiverVideoIntroSeen', 'true');
      }, 200);
    }, 1000);
  };

  const handleSkipVideo = () => {
    setIsVideoFadingOut(true);
    // Wait for video fade out, then start homepage fade in sequence
    setTimeout(() => {
      setShowVideoIntro(false);
      // Small delay before starting homepage fade in
      setTimeout(() => {
        setIsHomepageFadingIn(true);
        setHasSeenVideo(true);
        sessionStorage.setItem('bigRiverVideoIntroSeen', 'true');
      }, 200);
    }, 1000);
  };

  // Homepage body class
  useEffect(() => {
    if (typeof window === 'undefined') return;

    // Add homepage class to body
    document.body.classList.add('homepage');

    // Cleanup
    return () => {
      document.body.classList.remove('homepage');
    };
  }, []);

  return (
    <>
      {/* Video Intro Overlay - Desktop Only - Positioned to cover everything */}
      {showVideoIntro && (
        <VideoIntro
          onVideoEnd={handleVideoEnd}
          onSkip={handleSkipVideo}
        />
      )}

      {/* Main Content */}
      <div className={`home transition-opacity duration-1000 ease-in-out ${
        showVideoIntro
          ? 'opacity-0'
          : isHomepageFadingIn
            ? 'opacity-100'
            : 'opacity-0'
      }`} style={{
        margin: 0,
        padding: 0,
        width: '100vw',
        minHeight: '100vh',
        overflow: 'hidden',
        position: 'relative'
      }}>

        {/* Homepage styles are now loaded via CSS file to prevent hydration mismatches */}

        {/* Conditional Hero Section based on screen size */}
        {width < 768 ? (
          <MobileFigmaHeroSection />
        ) : (
          <FigmaHeroSection width={width} />
        )}
      </div>
    </>
  );
}

function MobileFigmaHeroSection() {
  return (
    <section className="relative w-screen h-screen overflow-hidden bg-white" style={{ margin: 0, padding: 0 }}>
      {/* Account Buttons - Under Sale Banner */}
      <div className="absolute top-16 right-4 z-20 flex gap-2">
        <Link
          to="/account/login"
          className="bg-black/60 hover:bg-red-500 text-white px-1.5 py-1 rounded text-xs font-medium transition-all duration-300 ease-out hover:scale-105"
        >
          Login
        </Link>
        <Link
          to="/account/register"
          className="bg-[#3a5c5c] hover:bg-[#2d4747] text-white px-1.5 py-1 rounded text-xs font-medium transition-transform duration-300 ease-out hover:scale-105 hover:-translate-y-0.5"
        >
          Sign Up
        </Link>
      </div>

      {/* Hero Background Video - Optimized */}
      <div className="absolute inset-0 w-full h-full">
        <OptimizedVideo
          src="/newhomepage/bg_video/bg_video.mp4"
          fallbackImage="/homescreenherosection.webp"
          className="w-full h-full object-cover object-center"
          autoPlay={true}
          muted={true}
          loop={true}
          playsInline={true}
          preload="metadata"
          lazy={false} // Don't lazy load hero video
        />
      </div>

      {/* Mobile Content Container - Centered vertically */}
      <div className="absolute inset-0 flex items-center justify-center px-4">
        <div className="relative w-full max-w-sm">

          {/* Logo Container */}
          <div className="relative mb-4">
            <div
              className="bg-[#3a5c5c] border border-black rounded-t-[20px] overflow-hidden"
              style={{
                width: '100%',
                height: '152px',
                boxShadow: '0px 4px 4px 0px rgba(0,0,0,0.25)'
              }}
            >
              <img
                src="/newhomepage/Big_River_Coffee_Rectangle_Logo.png"
                alt="Big River Coffee - Wild & Free"
                className="w-full h-full object-cover"
                style={{ objectFit: 'cover', objectPosition: 'center' }}
              />
            </div>
          </div>

          {/* Action Buttons - Vertical Stack */}
          <div className="space-y-0.5 mb-2">
            {/* Shop Coffee Button */}
            <Link
              to="/collections/all"
              className="block w-full group transition-transform duration-300 ease-out hover:scale-105 hover:-translate-y-1"
            >
              <div className="bg-[#3a5c5c] border border-black h-14 transition-all duration-300 group-hover:bg-[#2d4747] group-hover:border-[#2d4747] relative">
                <div
                  className="absolute bg-[#eeedc1] flex items-center justify-center text-black text-base font-medium transition-all duration-300 group-hover:bg-[#f5f4d1] group-hover:text-[#2d4747] group-hover:font-semibold"
                  style={{
                    left: '6px',
                    top: '6px',
                    right: '6px',
                    bottom: '6px'
                  }}
                >
                  SHOP COFFEE →
                </div>
              </div>
            </Link>

            {/* Learn Our Story Button */}
            <Link
              to="/our-story"
              className="block w-full group transition-transform duration-300 ease-out hover:scale-105 hover:-translate-y-1"
            >
              <div className="bg-[#3a5c5c] border border-black h-14 transition-all duration-300 group-hover:bg-[#2d4747] group-hover:border-[#2d4747] relative">
                <div
                  className="absolute bg-[#eeedc1] flex items-center justify-center text-black text-base font-medium transition-all duration-300 group-hover:bg-[#f5f4d1] group-hover:text-[#2d4747] group-hover:font-semibold"
                  style={{
                    left: '6px',
                    top: '6px',
                    right: '6px',
                    bottom: '6px'
                  }}
                >
                  LEARN OUR STORY →
                </div>
              </div>
            </Link>

            {/* Connect With Us Button */}
            <Link
              to="/contact"
              className="block w-full group transition-transform duration-300 ease-out hover:scale-105 hover:-translate-y-1"
            >
              <div className="bg-[#3a5c5c] border border-black h-14 transition-all duration-300 group-hover:bg-[#2d4747] group-hover:border-[#2d4747] relative">
                <div
                  className="absolute bg-[#eeedc1] flex items-center justify-center text-black text-base font-medium transition-all duration-300 group-hover:bg-[#f5f4d1] group-hover:text-[#2d4747] group-hover:font-semibold"
                  style={{
                    left: '6px',
                    top: '6px',
                    right: '6px',
                    bottom: '6px'
                  }}
                >
                  CONNECT WITH US →
                </div>
              </div>
            </Link>
          </div>

          {/* Mel Blount Banner */}
          <a
            href="https://melblount.org/"
            target="_blank"
            rel="noopener noreferrer"
            className="block w-full group transition-all duration-300 ease-out hover:scale-105 cursor-pointer"
          >
            {/* Dark Background */}
            <div className="bg-[#282424] rounded-b-[20px] transition-all duration-300 group-hover:bg-[#1a1a1a] relative h-14">
              {/* Yellow Banner */}
              <div
                className="absolute bg-[#f2dd00] rounded-b-[20px] flex items-center justify-center transition-all duration-300 group-hover:bg-[#f5e633]"
                style={{
                  left: '4px',
                  top: '4px',
                  right: '4px',
                  bottom: '4px'
                }}
              >
                <img
                  src="/newhomepage/MELBlount_LOGO.png"
                  alt="Mel Blount Foundation"
                  className="h-8 object-contain transition-all duration-300 group-hover:brightness-110"
                />
              </div>
            </div>
          </a>
        </div>
      </div>
    </section>
  );
}

function FigmaHeroSection({ width }: { width: number }) {
  return (
    <section className="relative w-screen h-screen overflow-hidden bg-white" style={{ margin: 0, padding: 0 }}>
      {/* Account Buttons - Top Right */}
      <div className="absolute top-4 right-4 z-20 flex gap-2">
        <Link
          to="/account/login"
          className="bg-white/90 hover:bg-red-500 hover:text-white text-black px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 ease-out border border-black/20 hover:border-black/40 hover:scale-105"
        >
          Login
        </Link>
        <Link
          to="/account/register"
          className="bg-[#3a5c5c] hover:bg-[#2d4747] text-white px-4 py-2 rounded-lg text-sm font-medium transition-transform duration-300 ease-out hover:scale-105 hover:-translate-y-0.5"
        >
          Sign Up
        </Link>
      </div>

      {/* Hero Background Video - Optimized */}
      <div className="absolute inset-0 w-full h-full">
        <OptimizedVideo
          src="/newhomepage/bg_video/bg_video.mp4"
          fallbackImage="/homescreenherosection.webp"
          className="w-full h-full object-cover object-center"
          autoPlay={true}
          muted={true}
          loop={true}
          playsInline={true}
          preload="metadata"
          lazy={false} // Don't lazy load hero video
        />
      </div>

      {/* Main Content Container - Scaled down and positioned */}
      <div className="absolute inset-0 flex items-center justify-center">
        <div className="relative" style={{ width: '540px', height: '480px' }}>
          {/* Logo Container - Scaled down */}
          <div
            className="absolute bg-[#3a5c5c] border border-black rounded-t-[20px]"
            style={{
              left: '0px',
              top: '0px',
              width: '540px',
              height: '245px',
              boxShadow: '0px 4px 4px 0px rgba(0,0,0,0.25)'
            }}
          >
            <img
              src="/newhomepage/Big_River_Coffee_Rectangle_Logo.png"
              alt="Big River Coffee Logo"
              className="w-full h-full object-cover rounded-t-[20px]"
              style={{ objectFit: 'cover', objectPosition: 'center' }}
            />
          </div>

          {/* Action Buttons Row - Exact Figma positioning */}

          {/* Shop Coffee Button */}
          <Link
            to="/collections/all"
            className="group absolute transition-transform duration-300 ease-out hover:scale-105 hover:-translate-y-2"
            style={{
              left: '0.7px',
              top: '250px',
              width: '181px',
              height: '153px'
            }}
          >
            <div className="bg-[#3a5c5c] border border-black w-full h-full transition-all duration-300 group-hover:bg-[#2d4747] group-hover:border-[#2d4747] rounded-lg overflow-hidden">
              {/* Coffee Image */}
              <div
                className="absolute transition-all duration-300 group-hover:scale-110"
                style={{
                  left: '8px',
                  top: '10px',
                  width: '164px',
                  height: '97px'
                }}
              >
                <img
                  src="/newhomepage/shop_coffee.png"
                  alt="Shop Coffee"
                  className="w-full h-full object-cover transition-all duration-300 group-hover:brightness-110"
                />
              </div>
              {/* Button Text */}
              <div
                className="absolute bg-[#eeedc1] flex items-center justify-center text-black text-lg font-medium transition-all duration-300 group-hover:bg-[#f5f4d1] group-hover:text-[#2d4747] group-hover:font-semibold"
                style={{
                  left: '8px',
                  top: '107px',
                  width: '164px',
                  height: '36px'
                }}
              >
                Shop Coffee
              </div>
            </div>
          </Link>

          {/* Learn Our Story Button */}
          <Link
            to="/our-story"
            className="group absolute transition-transform duration-300 ease-out hover:scale-105 hover:-translate-y-2"
            style={{
              left: '187px',
              top: '250px',
              width: '171px',
              height: '153px'
            }}
          >
            <div className="bg-[#3a5c5c] border border-black w-full h-full transition-all duration-300 group-hover:bg-[#2d4747] group-hover:border-[#2d4747] rounded-lg overflow-hidden">
              {/* Story Image */}
              <div
                className="absolute transition-all duration-300 group-hover:scale-110"
                style={{
                  left: '8px',
                  top: '10px',
                  width: '155px',
                  height: '95px'
                }}
              >
                <img
                  src="/newhomepage/our_story.png"
                  alt="Our Story"
                  className="w-full h-full object-cover transition-all duration-300 group-hover:brightness-110"
                />
              </div>
              {/* Button Text */}
              <div
                className="absolute bg-[#eeedc1] flex items-center justify-center text-black text-lg font-medium transition-all duration-300 group-hover:bg-[#f5f4d1] group-hover:text-[#2d4747] group-hover:font-semibold"
                style={{
                  left: '8px',
                  top: '107px',
                  width: '155px',
                  height: '36px'
                }}
              >
                Learn Our Story
              </div>
            </div>
          </Link>

          {/* Connect With Us Button */}
          <Link
            to="/contact"
            className="group absolute transition-all duration-300 ease-out hover:scale-105 hover:-translate-y-2"
            style={{
              left: '364px',
              top: '250px',
              width: '175px',
              height: '153px'
            }}
          >
            <div className="bg-[#3a5c5c] border border-black w-full h-full transition-all duration-300 group-hover:bg-[#2d4747] group-hover:border-[#2d4747] rounded-lg overflow-hidden">
              {/* Social Media Image */}
              <div
                className="absolute transition-all duration-300 group-hover:scale-110"
                style={{
                  left: '9px',
                  top: '10px',
                  width: '159px',
                  height: '97px'
                }}
              >
                <img
                  src="/newhomepage/social_media.png"
                  alt="Connect With Us"
                  className="w-full h-full object-cover transition-all duration-300 group-hover:brightness-110"
                />
              </div>
              {/* Button Text */}
              <div
                className="absolute bg-[#eeedc1] flex items-center justify-center text-black text-lg font-medium transition-all duration-300 group-hover:bg-[#f5f4d1] group-hover:text-[#2d4747] group-hover:font-semibold"
                style={{
                  left: '9px',
                  top: '107px',
                  width: '159px',
                  height: '36px'
                }}
              >
                Connect With Us
              </div>
            </div>
          </Link>

          {/* Mel Blount Banner - Scaled down and closer - Now Clickable */}
          <a
            href="https://melblount.org/"
            target="_blank"
            rel="noopener noreferrer"
            className="absolute group transition-all duration-300 ease-out hover:scale-105 cursor-pointer"
            style={{
              left: '0px',
              top: '408px',
              width: '540px',
              height: '68px'
            }}
          >
            {/* Dark Background */}
            <div
              className="absolute bg-[#282424] rounded-b-[20px] transition-all duration-300 group-hover:bg-[#1a1a1a]"
              style={{
                left: '0px',
                top: '0px',
                width: '540px',
                height: '68px'
              }}
            >
              {/* Yellow Banner */}
              <div
                className="absolute bg-[#f2dd00] rounded-b-[20px] flex items-center justify-between px-4 transition-all duration-300 group-hover:bg-[#f5e633]"
                style={{
                  left: '8px',
                  top: '8px',
                  width: '526px',
                  height: '52px'
                }}
              >
                {/* Left Side Text */}
                <div className="flex items-center">
                  {/* Proud Partner Text */}
                  <div className="text-black text-[12px] font-medium transition-all duration-300 group-hover:font-semibold">
                    Proud Partner of<br />Big River Coffee
                  </div>
                </div>

                {/* Centered Mel Blount Logo */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <img
                    src="/newhomepage/MELBlount_LOGO.png"
                    alt="Mel Blount Foundation"
                    className="h-10 object-contain transition-all duration-300 group-hover:brightness-110"
                  />
                </div>

                {/* Right side spacer for balance */}
                <div className="w-20"></div>
              </div>
            </div>
          </a>
        </div>
      </div>
    </section>
  );
}



const FEATURED_COLLECTION_QUERY = `#graphql
  fragment FeaturedCollection on Collection {
    id
    title
    image {
      id
      url
      altText
      width
      height
    }
    handle
  }
  query FeaturedCollection($country: CountryCode, $language: LanguageCode)
    @inContext(country: $country, language: $language) {
    collections(first: 1, sortKey: UPDATED_AT, reverse: true) {
      nodes {
        ...FeaturedCollection
      }
    }
  }
` as const;

const RECOMMENDED_PRODUCTS_QUERY = `#graphql
  fragment RecommendedProduct on Product {
    id
    title
    handle
    priceRange {
      minVariantPrice {
        amount
        currencyCode
      }
    }
    featuredImage {
      id
      url
      altText
      width
      height
    }
  }
  query RecommendedProducts ($country: CountryCode, $language: LanguageCode)
    @inContext(country: $country, language: $language) {
    products(first: 4, sortKey: UPDATED_AT, reverse: true) {
      nodes {
        ...RecommendedProduct
      }
    }
  }
` as const;
